part of 'reels_cubit.dart';

abstract class ReelsState extends Equatable {
  const ReelsState();

  @override
  List<Object?> get props => [];
}

class ReelsInitial extends ReelsState {
  const ReelsInitial();
}

class ReelsLoading extends ReelsState {
  const ReelsLoading();
}

class ReelsLoaded extends ReelsState {
  final List<Map<String, dynamic>> reels;
  final List<Map<String, dynamic>> filteredReels;
  final bool hasMore;
  final bool isLoadingMore;
  final List<Map<String, dynamic>> categories;
  final List<Map<String, dynamic>> propertyTypes;
  final City? selectedCity;
  final int? selectedCategoryId;
  final int? selectedPropertyTypeId;
  final String? searchQuery;

  const ReelsLoaded({
    required this.reels,
    required this.filteredReels,
    this.hasMore = false,
    this.isLoadingMore = false,
    this.categories = const [],
    this.propertyTypes = const [],
    this.selectedCity,
    this.selectedCategoryId,
    this.selectedPropertyTypeId,
    this.searchQuery,
  });

  ReelsLoaded copyWith({
    List<Map<String, dynamic>>? reels,
    List<Map<String, dynamic>>? filteredReels,
    bool? hasMore,
    bool? isLoadingMore,
    List<Map<String, dynamic>>? categories,
    List<Map<String, dynamic>>? propertyTypes,
    City? selectedCity,
    int? selectedCategoryId,
    int? selectedPropertyTypeId,
    String? searchQuery,
  }) {
    return ReelsLoaded(
      reels: reels ?? this.reels,
      filteredReels: filteredReels ?? this.filteredReels,
      hasMore: hasMore ?? this.hasMore,
      isLoadingMore: isLoadingMore ?? this.isLoadingMore,
      categories: categories ?? this.categories,
      propertyTypes: propertyTypes ?? this.propertyTypes,
      selectedCity: selectedCity ?? this.selectedCity,
      selectedCategoryId: selectedCategoryId ?? this.selectedCategoryId,
      selectedPropertyTypeId: selectedPropertyTypeId ?? this.selectedPropertyTypeId,
      searchQuery: searchQuery ?? this.searchQuery,
    );
  }

  @override
  List<Object?> get props => [
        reels,
        filteredReels,
        hasMore,
        isLoadingMore,
        categories,
        propertyTypes,
        selectedCity,
        selectedCategoryId,
        selectedPropertyTypeId,
        searchQuery,
      ];
}

class ReelsError extends ReelsState {
  final String message;

  const ReelsError(this.message);

  @override
  List<Object> get props => [message];
}
