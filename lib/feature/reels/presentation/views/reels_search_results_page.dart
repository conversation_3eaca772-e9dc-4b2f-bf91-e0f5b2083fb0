import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gather_point/core/databases/api/dio_consumer.dart';
import 'package:gather_point/core/styles/app_colors.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/styles/theme_helper.dart';
import 'package:gather_point/core/managers/locale_cubit/locale_cubit.dart';
import 'package:gather_point/feature/reels/presentation/views/widgets/reels_search_grid.dart';
import 'package:gather_point/feature/reels/presentation/views/reels_page.dart';
import 'package:gather_point/generated/l10n.dart';

class ReelsSearchResultsPage extends StatefulWidget {
  final String searchQuery;
  final List<Map<String, dynamic>> searchResults;
  final DioConsumer dioConsumer;
  final int serviceCategoryId;

  const ReelsSearchResultsPage({
    super.key,
    required this.searchQuery,
    required this.searchResults,
    required this.dioConsumer,
    this.serviceCategoryId = 2,
  });

  @override
  State<ReelsSearchResultsPage> createState() => _ReelsSearchResultsPageState();
}

class _ReelsSearchResultsPageState extends State<ReelsSearchResultsPage> {
  late List<Map<String, dynamic>> searchResults;
  bool isLoading = false;

  @override
  void initState() {
    super.initState();
    searchResults = List.from(widget.searchResults);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final s = S.of(context);
    final isRTL = context.read<LocaleCubit>().isArabic();

    // Set status bar style
    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle(
        statusBarBrightness: isDark ? Brightness.dark : Brightness.light,
        statusBarIconBrightness: isDark ? Brightness.light : Brightness.dark,
        systemNavigationBarColor: isDark ? Colors.black : theme.scaffoldBackgroundColor,
        systemNavigationBarIconBrightness: isDark ? Brightness.light : Brightness.dark,
      ),
    );

    return Scaffold(
      backgroundColor: isDark ? Colors.black : theme.scaffoldBackgroundColor,
      body: SafeArea(
        child: Column(
          children: [
            // Custom App Bar
            _buildAppBar(context, s, isDark, isRTL),
            
            // Search Results Grid
            Expanded(
              child: ReelsSearchGrid(
                searchResults: searchResults,
                onReelTap: _onReelTap,
                onRefresh: _onRefresh,
                isLoading: isLoading,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAppBar(BuildContext context, S s, bool isDark, bool isRTL) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: isDark ? Colors.black : Theme.of(context).scaffoldBackgroundColor,
        border: Border(
          bottom: BorderSide(
            color: isDark ? Colors.grey[800]! : Colors.grey[200]!,
            width: 0.5,
          ),
        ),
      ),
      child: Row(
        children: [
          // Back button
          IconButton(
            onPressed: () => Navigator.pop(context),
            icon: Icon(
              Icons.arrow_back_ios_rounded,
              color: isDark ? Colors.white : Theme.of(context).iconTheme.color,
              size: 20,
            ),
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(
              minWidth: 32,
              minHeight: 32,
            ),
          ),
          
          const SizedBox(width: 8),
          
          // Search query and results count
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '"${widget.searchQuery}"',
                  style: AppTextStyles.font16Bold.copyWith(
                    color: ThemeHelper.getPrimaryTextColor(context),
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                Text(
                  '${searchResults.length} ${searchResults.length == 1 ? 'result' : 'results'}',
                  style: AppTextStyles.font12Regular.copyWith(
                    color: ThemeHelper.getSecondaryTextColor(context),
                  ),
                ),
              ],
            ),
          ),
          
          // Search button to modify search
          IconButton(
            onPressed: () {
              // Navigate back to search
              Navigator.pop(context);
            },
            icon: Icon(
              Icons.search,
              color: isDark ? Colors.white : Theme.of(context).iconTheme.color,
              size: 20,
            ),
            tooltip: s.search,
          ),
        ],
      ),
    );
  }

  void _onReelTap(Map<String, dynamic> selectedReel, int selectedIndex) {
    // Navigate to full-screen reels player with the search results
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ReelsPage(
          searchResults: searchResults,
          searchQuery: widget.searchQuery,
          serviceCategoryId: widget.serviceCategoryId,
          selectedItem: selectedReel,
          showBackButton: true,
        ),
      ),
    );
  }

  Future<void> _onRefresh() async {
    setState(() {
      isLoading = true;
    });

    try {
      // Add haptic feedback
      HapticFeedback.mediumImpact();

      // Perform search again with the same query
      final response = await widget.dioConsumer.get(
        '/api/items/search',
        queryParameters: {
          'service_category_id': widget.serviceCategoryId,
          'keyword': widget.searchQuery.trim(),
          'reels': 1,
          'has_video': 1, // Only return items with videos
          'page': 1,
          'limit': 50, // Get more results for grid view
        },
      );

      if (response['success'] == true && response['data'] != null) {
        final data = response['data'];
        final List<Map<String, dynamic>> newResults =
            List<Map<String, dynamic>>.from(data['items'] ?? []);

        setState(() {
          searchResults = newResults;
          isLoading = false;
        });

        // Success haptic feedback
        HapticFeedback.lightImpact();
      } else {
        setState(() {
          isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        isLoading = false;
      });
      
      // Error haptic feedback
      HapticFeedback.heavyImpact();
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to refresh search results: ${e.toString()}'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }
}
