import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:gather_point/core/styles/app_colors.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/styles/theme_helper.dart';
import 'package:gather_point/generated/l10n.dart';

class ReelsSearchGrid extends StatelessWidget {
  final List<Map<String, dynamic>> searchResults;
  final Function(Map<String, dynamic>, int) onReelTap;
  final Future<void> Function()? onRefresh;
  final bool isLoading;

  const ReelsSearchGrid({
    super.key,
    required this.searchResults,
    required this.onReelTap,
    this.onRefresh,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final s = S.of(context);

    if (isLoading) {
      return _buildLoadingGrid(context);
    }

    if (searchResults.isEmpty) {
      return _buildEmptyState(context, s, isDark);
    }

    return RefreshIndicator(
      onRefresh: onRefresh ?? () async {},
      color: AppColors.yellow,
      backgroundColor: isDark ? Colors.black87 : Colors.white,
      child: CustomScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        slivers: [
          SliverPadding(
            padding: const EdgeInsets.all(8.0),
            sliver: SliverGrid(
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2, // 2 columns for masonry layout
                crossAxisSpacing: 12,
                mainAxisSpacing: 12,
                childAspectRatio: 0.75, // Adjusted for masonry effect
              ),
              delegate: SliverChildBuilderDelegate(
                (context, index) {
                  final reel = searchResults[index];
                  return ReelGridItem(
                    reel: reel,
                    index: index,
                    onTap: () => onReelTap(reel, index),
                  );
                },
                childCount: searchResults.length,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingGrid(BuildContext context) {
    return GridView.builder(
      padding: const EdgeInsets.all(8.0),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        crossAxisSpacing: 4,
        mainAxisSpacing: 4,
        childAspectRatio: 9 / 16,
      ),
      itemCount: 12, // Show 12 shimmer items
      itemBuilder: (context, index) {
        return const ReelGridItemShimmer();
      },
    );
  }

  Widget _buildEmptyState(BuildContext context, S s, bool isDark) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off_rounded,
              size: 64,
              color: isDark ? Colors.grey : Colors.grey[600],
            ),
            const SizedBox(height: 16),
            Text(
              s.noResults,
              style: AppTextStyles.font18Bold.copyWith(
                color: ThemeHelper.getPrimaryTextColor(context),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              s.tryDifferentSearch,
              style: AppTextStyles.font14Regular.copyWith(
                color: ThemeHelper.getSecondaryTextColor(context),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}

class ReelGridItem extends StatefulWidget {
  final Map<String, dynamic> reel;
  final int index;
  final VoidCallback onTap;

  const ReelGridItem({
    super.key,
    required this.reel,
    required this.index,
    required this.onTap,
  });

  @override
  State<ReelGridItem> createState() => _ReelGridItemState();
}

class _ReelGridItemState extends State<ReelGridItem>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));

    // Start animation with delay based on index
    Future.delayed(Duration(milliseconds: widget.index * 50), () {
      if (mounted) {
        _animationController.forward();
      }
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return ScaleTransition(
          scale: _scaleAnimation,
          child: GestureDetector(
            onTapDown: (_) => _onTapDown(),
            onTapUp: (_) => _onTapUp(),
            onTapCancel: () => _onTapCancel(),
            onTap: () {
              HapticFeedback.lightImpact();
              widget.onTap();
            },
            child: AnimatedScale(
              scale: _isPressed ? 0.95 : 1.0,
              duration: const Duration(milliseconds: 100),
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  color: isDark ? Colors.grey[900] : Colors.grey[200],
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Stack(
                    fit: StackFit.expand,
                    children: [
                      // Thumbnail/Image
                      _buildThumbnail(context),
                      
                      // Gradient overlay
                      Container(
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors: [
                              Colors.transparent,
                              Colors.black.withValues(alpha: 0.7),
                            ],
                            stops: const [0.6, 1.0],
                          ),
                        ),
                      ),
                      
                      // Play icon
                      const Center(
                        child: Icon(
                          Icons.play_circle_outline,
                          color: Colors.white,
                          size: 32,
                        ),
                      ),
                      
                      // Bottom info
                      Positioned(
                        bottom: 8,
                        left: 8,
                        right: 8,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            // Views count (if available)
                            if (widget.reel['views_count'] != null)
                              Row(
                                children: [
                                  const Icon(
                                    Icons.play_arrow,
                                    color: Colors.white,
                                    size: 12,
                                  ),
                                  const SizedBox(width: 2),
                                  Text(
                                    _formatCount(widget.reel['views_count']),
                                    style: AppTextStyles.font10Bold.copyWith(
                                      color: Colors.white,
                                    ),
                                  ),
                                ],
                              ),
                            
                            // Title (truncated)
                            if (widget.reel['title'] != null)
                              Text(
                                (widget.reel['title'] as String?) ?? '',
                                style: AppTextStyles.font10Regular.copyWith(
                                  color: Colors.white,
                                ),
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  void _onTapDown() {
    setState(() => _isPressed = true);
  }

  void _onTapUp() {
    setState(() => _isPressed = false);
  }

  void _onTapCancel() {
    setState(() => _isPressed = false);
  }

  Widget _buildThumbnail(BuildContext context) {
    final imageUrl = widget.reel['image'] as String?;
    final thumbnailUrl = widget.reel['thumbnail'] as String?;
    final displayUrl = thumbnailUrl ?? imageUrl;

    if (displayUrl != null && displayUrl.isNotEmpty) {
      return CachedNetworkImage(
        imageUrl: displayUrl,
        fit: BoxFit.cover,
        placeholder: (context, url) => _buildPlaceholder(context),
        errorWidget: (context, url, error) => _buildPlaceholder(context),
        memCacheWidth: 300, // Limit memory cache size
        memCacheHeight: 300,
        maxWidthDiskCache: 600, // Limit disk cache size
        maxHeightDiskCache: 600,
      );
    }

    return _buildPlaceholder(context);
  }

  Widget _buildPlaceholder(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    
    return Container(
      color: isDark ? Colors.grey[800] : Colors.grey[300],
      child: const Center(
        child: Icon(
          Icons.video_library_outlined,
          color: Colors.white54,
          size: 24,
        ),
      ),
    );
  }

  String _formatCount(dynamic count) {
    if (count == null) return '0';
    final num = (count is int) ? count : int.tryParse(count.toString()) ?? 0;
    
    if (num >= 1000000) {
      return '${(num / 1000000).toStringAsFixed(1)}M';
    } else if (num >= 1000) {
      return '${(num / 1000).toStringAsFixed(1)}K';
    }
    return num.toString();
  }
}

class ReelGridItemShimmer extends StatefulWidget {
  const ReelGridItemShimmer({super.key});

  @override
  State<ReelGridItemShimmer> createState() => _ReelGridItemShimmerState();
}

class _ReelGridItemShimmerState extends State<ReelGridItemShimmer>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    )..repeat();
    
    _animation = Tween<double>(
      begin: -1.0,
      end: 2.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            color: isDark ? Colors.grey[900] : Colors.grey[200],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: Stack(
              children: [
                Container(
                  color: isDark ? Colors.grey[800] : Colors.grey[300],
                ),
                Positioned(
                  left: _animation.value * MediaQuery.of(context).size.width,
                  child: Container(
                    width: 100,
                    height: double.infinity,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          Colors.transparent,
                          (isDark ? Colors.grey[700] : Colors.grey[100])!
                              .withValues(alpha: 0.5),
                          Colors.transparent,
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
