import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:geolocator/geolocator.dart';
import 'package:gather_point/core/databases/api/dio_consumer.dart';
import 'package:gather_point/core/services/service_locator.dart';
import 'package:gather_point/core/styles/app_colors.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/styles/theme_helper.dart';
import 'package:gather_point/core/managers/locale_cubit/locale_cubit.dart';
import 'package:gather_point/core/utils/app_assets.dart';
import 'package:gather_point/feature/auth/Domain/Entities/user_entity.dart';
import 'package:gather_point/feature/reels/presentation/views/widgets/video_player_widget.dart';
import 'package:gather_point/feature/reels/presentation/views/widgets/reels_search_grid.dart';
import 'package:gather_point/feature/reels/presentation/views/reels_search_results_page.dart';
import 'package:gather_point/feature/reels/presentation/cubit/reels_cubit.dart';
import 'package:gather_point/feature/home/<USER>/Data%20Sources/Models/City.dart';
import 'package:gather_point/generated/l10n.dart';
import 'package:hive/hive.dart';

class ReelsPage extends StatefulWidget {
  final List<Map<String, dynamic>> searchResults;
  final String searchQuery;
  final int serviceCategoryId;
  final Map<String, dynamic>? selectedItem; // The item clicked from home
  final bool showBackButton; // Whether to show back button

  const ReelsPage({
    super.key,
    required this.searchResults,
    required this.searchQuery,
    required this.serviceCategoryId,
    this.selectedItem,
    this.showBackButton = false,
  });

  @override
  State<ReelsPage> createState() => _ReelsPageState();
}

class _ReelsPageState extends State<ReelsPage> {
  List<Map<String, dynamic>> videoData = [];
  List<Map<String, dynamic>> filteredVideoData = [];
  bool isLoading = true;
  bool hasError = false;
  bool hasInitiallyLoaded = false; // Track if data has been loaded at least once
  late final DioConsumer dioConsumer;
  late final ReelsCubit _reelsCubit;
  late final PageController _pageController;
  int _selectedItemIndex = 0;

  // Search and Filter
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  String _selectedCategory = '';
  String _sortBy = 'newest';
  bool _showSearchBar = false;

  // Filter state
  City? selectedCity;
  List<City> cities = [];
  int? selectedCategoryId;
  int? selectedPropertyTypeId;
  List<Map<String, dynamic>> categories = [];
  List<Map<String, dynamic>> propertyTypes = [];

  @override
  void initState() {
    super.initState();
    dioConsumer = DioConsumer(
      dio: getIt<Dio>(),
      profileBox: getIt<Box<UserEntity>>(),
    );

    // Initialize cubit
    _reelsCubit = ReelsCubit(dioConsumer);

    // Initialize PageController with initial page
    _pageController = PageController(initialPage: 0);

    if (widget.searchResults.isNotEmpty) {
      setState(() {
        videoData = widget.searchResults;
        filteredVideoData = List.from(videoData);
        isLoading = false;
        hasError = false;
        hasInitiallyLoaded = true; // Mark as loaded since we have search results
      });
      _findSelectedItemIndex();
      // Don't load metadata for search results - they're already complete
    } else {
      _initializeData();
      // Only force load metadata when not using search results
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _forceLoadMetadata();
      });
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    _pageController.dispose();
    _reelsCubit.close();
    super.dispose();
  }

  /// Initialize data by loading cities and then reels
  Future<void> _initializeData() async {
    await _loadCities();

    // Load categories and property types first
    await _loadCategoriesAndPropertyTypes();

    // Then initialize reels
    await _reelsCubit.initializeReels(
      serviceCategoryId: widget.serviceCategoryId,
      selectedCity: selectedCity,
    );
  }

  /// Get current device location
  Future<Map<String, double>> _getCurrentLocation() async {
    try {
      // Check if location services are enabled
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        debugPrint('Location services are disabled. Using default Riyadh coordinates.');
        return {'lat': 24.7136, 'lng': 46.6753};
      }

      // Check location permissions
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          debugPrint('Location permissions are denied. Using default Riyadh coordinates.');
          return {'lat': 24.7136, 'lng': 46.6753};
        }
      }

      if (permission == LocationPermission.deniedForever) {
        debugPrint('Location permissions are permanently denied. Using default Riyadh coordinates.');
        return {'lat': 24.7136, 'lng': 46.6753};
      }

      // Get current position
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
        timeLimit: const Duration(seconds: 10),
      );

      debugPrint('Device location obtained: ${position.latitude}, ${position.longitude}');
      return {'lat': position.latitude, 'lng': position.longitude};

    } catch (e) {
      debugPrint('Error getting device location: $e. Using default Riyadh coordinates.');
      return {'lat': 24.7136, 'lng': 46.6753};
    }
  }

  /// Load cities for filtering using device location
  Future<void> _loadCities() async {
    try {
      // Get device location first
      final locationData = await _getCurrentLocation();

      final response = await dioConsumer.post(
        '/api/general/cities',
        data: {
          'lat': locationData['lat'],
          'lng': locationData['lng'],
        },
      );

      if (response['data'] != null) {
        final data = response['data']['cities'] as List;
        setState(() {
          cities = data.map((e) => City.fromJson(e)).toList();
          if (cities.isNotEmpty) {
            selectedCity = cities.first;
          }
        });
        debugPrint('Cities loaded with device location: ${locationData['lat']}, ${locationData['lng']}');
        debugPrint('Selected city: ${selectedCity?.name} (ID: ${selectedCity?.id})');
      }
    } catch (e) {
      debugPrint('Error loading cities: $e');
    }
  }

  /// Load categories and property types
  Future<void> _loadCategoriesAndPropertyTypes() async {
    try {
      debugPrint('Loading categories and property types...');
      await _reelsCubit.loadFilterMetadata(selectedCity?.id);
      debugPrint('Categories and property types loading completed');
    } catch (e) {
      debugPrint('Error loading categories and property types: $e');
    }
  }

  /// Force load metadata immediately
  Future<void> _forceLoadMetadata() async {
    debugPrint('Force loading metadata...');
    try {
      // Test direct API calls
      await _testDirectApiCalls();

      await _reelsCubit.loadFilterMetadata(selectedCity?.id);
      debugPrint('Force load completed');

      // Force UI update
      if (mounted) {
        setState(() {
          // Trigger rebuild
        });
      }
    } catch (e) {
      debugPrint('Force load failed: $e');
    }
  }

  /// Test direct API calls
  Future<void> _testDirectApiCalls() async {
    try {
      debugPrint('Testing direct API calls...');

      // Test categories API with subcategories
      final categoriesResponse = await dioConsumer.get(
        '/api/service_categories/list',
        queryParameters: {'id': 1},
      );
      debugPrint('Direct categories API response: $categoriesResponse');

      // Test property types API
      final propertyTypesResponse = await dioConsumer.get('/api/property_types/list');
      debugPrint('Direct property types API response: $propertyTypesResponse');

    } catch (e) {
      debugPrint('Direct API test failed: $e');
    }
  }



  /// Find the index of the selected item in the filtered data
  void _findSelectedItemIndex() {
    if (widget.selectedItem != null) {
      final selectedId = widget.selectedItem!['id'];
      final index = filteredVideoData.indexWhere((item) => item['id'] == selectedId);
      if (index != -1) {
        _selectedItemIndex = index;
        // Update PageController to start at the selected item
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (_pageController.hasClients) {
            _pageController.jumpToPage(_selectedItemIndex);
          }
        });
      }
    }
  }

  Future<void> fetchReels() async {
    try {
      if (!mounted) return;
      setState(() {
        isLoading = true;
        hasError = false;
      });

      final response = await dioConsumer.get(
        '/api/items/list',
        queryParameters: {
          'service_category_id': widget.serviceCategoryId,
          'reels': 1
        },
      );

      if (!mounted) return;
      if (response['success'] == true && response['data'] != null) {
        final data = response['data'];
        final List<Map<String, dynamic>> items =
            List<Map<String, dynamic>>.from(data['items'] ?? []);

        setState(() {
          videoData = items;
          filteredVideoData = List.from(videoData);
          isLoading = false;
          hasError = false;
        });
        _applyFilters();
        _findSelectedItemIndex(); // Find selected item after fetching
      } else {
        setState(() {
          isLoading = false;
          hasError = true;
        });
      }
    } catch (e) {
      debugPrint("Error fetching reels: $e");
      if (!mounted) return;
      setState(() {
        isLoading = false;
        hasError = true;
      });
    }
  }

  void _applyFilters() {
    if (!mounted) return;
    setState(() {
      filteredVideoData = videoData.where((video) {
        // Search filter - search in multiple fields
        bool matchesSearch = _searchQuery.isEmpty ||
            ((video['title'] as String?) ?? '')
                    .toLowerCase()
                    .contains(_searchQuery.toLowerCase()) ||
            ((video['description'] as String?) ?? '')
                    .toLowerCase()
                    .contains(_searchQuery.toLowerCase()) ||
            ((video['location'] as String?) ?? '')
                    .toLowerCase()
                    .contains(_searchQuery.toLowerCase());

        // Category filter
        bool matchesCategory = _selectedCategory.isEmpty ||
            (video['category'] as String?) == _selectedCategory ||
            ((video['service_category'] as Map<String, dynamic>?)?['title'] as String?) == _selectedCategory;

        return matchesSearch && matchesCategory;
      }).toList();

      // Sort results
      _sortResults();
    });
  }

  void _sortResults() {
    try {
      switch (_sortBy) {
        case 'newest':
          filteredVideoData.sort((a, b) {
            try {
              final dateA =
                  DateTime.tryParse(a['created_at'] ?? '') ?? DateTime.now();
              final dateB =
                  DateTime.tryParse(b['created_at'] ?? '') ?? DateTime.now();
              return dateB.compareTo(dateA);
            } catch (e) {
              return 0;
            }
          });
          break;
        case 'oldest':
          filteredVideoData.sort((a, b) {
            try {
              final dateA =
                  DateTime.tryParse(a['created_at'] ?? '') ?? DateTime.now();
              final dateB =
                  DateTime.tryParse(b['created_at'] ?? '') ?? DateTime.now();
              return dateA.compareTo(dateB);
            } catch (e) {
              return 0;
            }
          });
          break;
        case 'mostLiked':
          filteredVideoData.sort((a, b) {
            final likesA =
                _parseInt(a['likes_count'] ?? a['favorite_count'] ?? 0);
            final likesB =
                _parseInt(b['likes_count'] ?? b['favorite_count'] ?? 0);
            return likesB.compareTo(likesA);
          });
          break;
        case 'mostCommented':
          filteredVideoData.sort((a, b) {
            final commentsA = _parseInt(a['comments_count'] ?? 0);
            final commentsB = _parseInt(b['comments_count'] ?? 0);
            return commentsB.compareTo(commentsA);
          });
          break;
      }
    } catch (e) {
      debugPrint('Error sorting results: $e');
      // If sorting fails, keep original order
    }
  }

  void _onSearchChanged(String query) {
    setState(() {
      _searchQuery = query;
    });
    // Don't trigger search automatically - only update the query
  }

  void _onSearchSubmitted() {
    final query = _searchController.text.trim();
    if (query.isNotEmpty) {
      _performTikTokStyleSearch(query);
    }
  }

  /// Perform TikTok-style search and navigate to grid results
  Future<void> _performTikTokStyleSearch(String query) async {
    bool isDialogShowing = false;

    try {
      // Show loading indicator
      if (mounted) {
        isDialogShowing = true;
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (dialogContext) => const PopScope(
            canPop: false,
            child: Center(
              child: CircularProgressIndicator(
                color: AppColors.yellow,
              ),
            ),
          ),
        );
      }

      // Perform search API call
      final response = await dioConsumer.get(
        '/api/items/search',
        queryParameters: {
          'service_category_id': widget.serviceCategoryId,
          'keyword': query,
          'reels': 1,
          'has_video': 1, // Only return items with videos
          'page': 1,
          'limit': 50, // Get more results for grid view
        },
      );

      // Safely dismiss loading dialog
      if (mounted && isDialogShowing) {
        Navigator.of(context, rootNavigator: true).pop();
        isDialogShowing = false;
      }

      if (response['success'] == true && response['data'] != null) {
        final data = response['data'];
        final List<Map<String, dynamic>> searchResults =
            List<Map<String, dynamic>>.from(data['items'] ?? []);

        // Navigate to TikTok-style search results page
        if (mounted) {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => ReelsSearchResultsPage(
                searchQuery: query,
                searchResults: searchResults,
                dioConsumer: dioConsumer,
                serviceCategoryId: widget.serviceCategoryId,
              ),
            ),
          );
        }
      } else {
        // Show no results message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(S.of(context).noResults),
              backgroundColor: Colors.orange,
              duration: const Duration(seconds: 2),
            ),
          );
        }
      }
    } catch (e) {
      // Safely dismiss loading dialog if still showing
      if (mounted && isDialogShowing) {
        try {
          Navigator.of(context, rootNavigator: true).pop();
        } catch (popError) {
          // Dialog might already be dismissed, ignore error
          debugPrint('Dialog already dismissed: $popError');
        }
        isDialogShowing = false;
      }

      // Show error message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Search failed: ${e.toString()}'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  void _showFilterDialog() {
    // Don't show filter dialog for search results
    if (widget.searchResults.isNotEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Filters are not available for search results'),
          backgroundColor: Colors.orange,
          duration: const Duration(seconds: 2),
        ),
      );
      return;
    }

    // Debug: Check if categories and property types are loaded
    debugPrint('Filter dialog - Categories: ${categories.length}, Property Types: ${propertyTypes.length}');
    debugPrint('Current cubit state: ${_reelsCubit.state}');

    // Force load categories and property types if not loaded
    if (categories.isEmpty || propertyTypes.isEmpty) {
      debugPrint('Categories or property types empty, forcing reload...');
      _loadCategoriesAndPropertyTypes();
    }

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => StatefulBuilder(
        builder: (context, setModalState) => _buildFilterDialog(setModalState),
      ),
    );
  }

  void _toggleSearchBar() {
    setState(() {
      _showSearchBar = !_showSearchBar;
      if (!_showSearchBar) {
        _searchController.clear();
        _searchQuery = '';
        _applyFilters();
      }
    });
  }

  /// Enhanced refresh functionality with haptic feedback
  Future<void> _onRefresh() async {
    // Don't refresh search results - they should remain static
    if (widget.searchResults.isNotEmpty) {
      HapticFeedback.lightImpact();
      return;
    }

    try {
      // Add haptic feedback
      HapticFeedback.mediumImpact();

      // Refresh reels data using cubit
      await _reelsCubit.refreshReels(
        serviceCategoryId: widget.serviceCategoryId,
        selectedCity: selectedCity,
        categoryId: selectedCategoryId,
        propertyTypeId: selectedPropertyTypeId,
      );

      // Add success haptic feedback
      HapticFeedback.lightImpact();
    } catch (e) {
      debugPrint('Refresh error: $e');
      // Add error haptic feedback
      HapticFeedback.heavyImpact();
    }
  }

  /// Helper method to safely parse price values that might be strings or numbers
  double _parsePrice(dynamic price) {
    if (price == null) return 0.0;
    if (price is double) return price;
    if (price is int) return price.toDouble();
    if (price is String) {
      return double.tryParse(price) ?? 0.0;
    }
    return 0.0;
  }

  /// Helper method to safely parse integer values that might be strings or numbers
  int _parseInt(dynamic value) {
    if (value == null) return 0;
    if (value is int) return value;
    if (value is double) return value.toInt();
    if (value is String) {
      return int.tryParse(value) ?? 0;
    }
    return 0;
  }

  Widget _buildLoading() {
    final theme = Theme.of(context);
    final s = S.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      color: isDark ? Colors.black : theme.scaffoldBackgroundColor,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
              width: 60,
              height: 60,
              child: CircularProgressIndicator(
                strokeWidth: 3,
                valueColor: AlwaysStoppedAnimation<Color>(
                  isDark ? Colors.white : theme.colorScheme.primary,
                ),
              ),
            ),
            const SizedBox(height: 24),
            Text(
              s.loadingReels,
              style: AppTextStyles.font16Regular.copyWith(
                color: isDark ? Colors.white : theme.textTheme.bodyLarge?.color,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildError([String? message]) {
    final theme = Theme.of(context);
    final s = S.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      color: isDark ? Colors.black : theme.scaffoldBackgroundColor,
      child: SafeArea(
        child: Column(
          children: [
            // Enhanced App Bar
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              child: Column(
                children: [
                  Row(
                    children: [
                      IconButton(
                        onPressed: () => Navigator.pop(context),
                        icon: Icon(
                          Icons.arrow_back_ios_rounded,
                          color: isDark ? Colors.white : theme.iconTheme.color,
                        ),
                      ),
                      Expanded(
                        child: Text(
                          s.reels,
                          style: AppTextStyles.font18Bold.copyWith(
                            color: isDark
                                ? Colors.white
                                : theme.textTheme.bodyLarge?.color,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      IconButton(
                        onPressed: _toggleSearchBar,
                        icon: Icon(
                          _showSearchBar ? Icons.close : Icons.search,
                          color: isDark ? Colors.white : theme.iconTheme.color,
                        ),
                        tooltip: s.searchReels,
                      ),
                      IconButton(
                        onPressed: _showFilterDialog,
                        icon: Icon(
                          Icons.filter_list,
                          color: isDark ? Colors.white : theme.iconTheme.color,
                        ),
                        tooltip: s.filterReels,
                      ),
                    ],
                  ),

                  // Search Bar
                  if (_showSearchBar)
                    Container(
                      margin: const EdgeInsets.only(top: 12),
                      child: TextField(
                        controller: _searchController,
                        onChanged: _onSearchChanged,
                        onSubmitted: (_) => _onSearchSubmitted(),
                        textInputAction: TextInputAction.search,
                        style: AppTextStyles.font14Regular.copyWith(
                          color: isDark
                              ? Colors.white
                              : theme.textTheme.bodyLarge?.color,
                        ),
                        decoration: InputDecoration(
                          hintText: s.searchHint,
                          hintStyle: AppTextStyles.font14Regular.copyWith(
                            color: isDark
                                ? Colors.grey[400]
                                : theme.textTheme.bodyMedium?.color,
                          ),
                          prefixIcon: Icon(
                            Icons.search,
                            color: isDark
                                ? Colors.grey[400]
                                : theme.iconTheme.color,
                          ),
                          suffixIcon: _searchQuery.isNotEmpty
                              ? Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    IconButton(
                                      onPressed: _onSearchSubmitted,
                                      icon: const Icon(
                                        Icons.search,
                                        color: AppColors.yellow,
                                      ),
                                      tooltip: s.search,
                                    ),
                                    IconButton(
                                      onPressed: () {
                                        _searchController.clear();
                                        _onSearchChanged('');
                                      },
                                      icon: Icon(
                                        Icons.clear,
                                        color: isDark
                                            ? Colors.grey[400]
                                            : theme.iconTheme.color,
                                      ),
                                    ),
                                  ],
                                )
                              : IconButton(
                                  onPressed: _onSearchSubmitted,
                                  icon: Icon(
                                    Icons.search,
                                    color: isDark
                                        ? Colors.grey[400]
                                        : theme.iconTheme.color,
                                  ),
                                  tooltip: s.search,
                                ),
                          filled: true,
                          fillColor:
                              isDark ? Colors.grey[800] : Colors.grey[100],
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide.none,
                          ),
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 12,
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),
            // Error Content
            Expanded(
              child: Center(
                child: Padding(
                  padding: const EdgeInsets.all(32),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                        padding: const EdgeInsets.all(24),
                        decoration: BoxDecoration(
                          color: Colors.red.withValues(alpha: 0.1),
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.error_outline_rounded,
                          color: Colors.red,
                          size: 64,
                        ),
                      ),
                      const SizedBox(height: 24),
                      Text(
                        message ?? s.failedToLoadReels,
                        style: AppTextStyles.font20Bold.copyWith(
                          color: isDark
                              ? Colors.white
                              : theme.textTheme.bodyLarge?.color,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 12),
                      Text(
                        s.checkConnection,
                        style: AppTextStyles.font14Regular.copyWith(
                          color: isDark
                              ? Colors.grey[400]
                              : theme.textTheme.bodyMedium?.color,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 32),
                      ElevatedButton.icon(
                        onPressed: () => _reelsCubit.refreshReels(
                          serviceCategoryId: widget.serviceCategoryId,
                          selectedCity: selectedCity,
                          categoryId: selectedCategoryId,
                          propertyTypeId: selectedPropertyTypeId,
                        ),
                        icon: const Icon(Icons.refresh_rounded),
                        label: Text(s.retry),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: theme.colorScheme.primary,
                          foregroundColor: theme.colorScheme.onPrimary,
                          padding: const EdgeInsets.symmetric(
                            horizontal: 32,
                            vertical: 16,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(25),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNoReelsFound() {
    final theme = Theme.of(context);
    final s = S.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: Colors.grey.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.video_library_outlined,
                color: Colors.grey[600],
                size: 64,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              s.noResults,
              style: AppTextStyles.font20Bold.copyWith(
                color: isDark
                    ? Colors.white
                    : theme.textTheme.bodyLarge?.color,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 12),
            Text(
              'Try adjusting your filters or search in a different area',
              style: AppTextStyles.font14Regular.copyWith(
                color: isDark
                    ? Colors.grey[400]
                    : theme.textTheme.bodyMedium?.color,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            ElevatedButton.icon(
              onPressed: () => _reelsCubit.refreshReels(
                serviceCategoryId: widget.serviceCategoryId,
                selectedCity: selectedCity,
              ),
              icon: const Icon(Icons.refresh_rounded),
              label: Text(s.clearFilters),
              style: ElevatedButton.styleFrom(
                backgroundColor: theme.colorScheme.primary,
                foregroundColor: theme.colorScheme.onPrimary,
                padding: const EdgeInsets.symmetric(
                  horizontal: 32,
                  vertical: 16,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(25),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNoResults() {
    final theme = Theme.of(context);
    final s = S.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      color: isDark ? Colors.black : theme.scaffoldBackgroundColor,
      child: SafeArea(
        child: Column(
          children: [
            // Enhanced App Bar
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              child: Row(
                children: [
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: Icon(
                      Icons.arrow_back_ios_rounded,
                      color: isDark ? Colors.white : theme.iconTheme.color,
                    ),
                  ),
                  Expanded(
                    child: Text(
                      s.searchResults,
                      style: AppTextStyles.font18Bold.copyWith(
                        color: isDark
                            ? Colors.white
                            : theme.textTheme.bodyLarge?.color,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  const SizedBox(width: 48), // Balance the back button
                ],
              ),
            ),
            // No Results Content
            Expanded(
              child: Center(
                child: Padding(
                  padding: const EdgeInsets.all(32),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                        padding: const EdgeInsets.all(24),
                        decoration: BoxDecoration(
                          color: Colors.grey.withValues(alpha: 0.1),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          Icons.search_off_rounded,
                          color: isDark ? Colors.grey : Colors.grey[600],
                          size: 64,
                        ),
                      ),
                      const SizedBox(height: 24),
                      Text(
                        s.noResults,
                        style: AppTextStyles.font20Bold.copyWith(
                          color: isDark
                              ? Colors.white
                              : theme.textTheme.bodyLarge?.color,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 12),
                      Text(
                        '${s.noSearchResults} "${widget.searchQuery}"',
                        style: AppTextStyles.font14Regular.copyWith(
                          color: isDark
                              ? Colors.grey[400]
                              : theme.textTheme.bodyMedium?.color,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 32),
                      ElevatedButton.icon(
                        onPressed: () => Navigator.pop(context),
                        icon: const Icon(Icons.arrow_back_rounded),
                        label: Text(s.backToSearch),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: theme.colorScheme.primary,
                          foregroundColor: theme.colorScheme.onPrimary,
                          padding: const EdgeInsets.symmetric(
                            horizontal: 32,
                            vertical: 16,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(25),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReels() {
    final s = S.of(context);
    final hasActiveFilters = _searchQuery.isNotEmpty || _sortBy != 'newest';
    final isRTL = context.read<LocaleCubit>().isArabic();

    return SafeArea(
      child: Stack(
        children: [
          // Main Video Content with RefreshIndicator
          RefreshIndicator(
            onRefresh: _onRefresh,
            color: AppColors.yellow,
            backgroundColor: Colors.black.withValues(alpha: 0.8),
            strokeWidth: 3,
            displacement: 60,
            child: PageView.builder(
              controller: _pageController,
              scrollDirection: Axis.vertical,
              itemCount: filteredVideoData.length,
              onPageChanged: (index) {
                // Update current index for video management
                setState(() {
                  _selectedItemIndex = index;
                });
              },
              itemBuilder: (context, index) {
                final item = filteredVideoData[index];
                return VideoPlayerWidget(
                  key: ValueKey('video_${item['id']}_$index'), // Unique key for proper disposal
                  facilities: (item['facilities'] as List?) ?? [],
                  videoUrl: (item['video'] as String?) ?? '',
                  title: (item['title'] as String?) ?? 'Untitled',
                  location: (item['title'] as String?) ?? 'Unknown Location',
                  id: (item['id'] as int?) ?? 0,
                  price: _parsePrice(item['price']),
                  serviceCategoryId: widget.serviceCategoryId,
                  favorite: (item['favorite'] as bool?) ?? false,
                  dioConsumer: dioConsumer,
                );
              },
            ),
          ),

          // Search and Filter Overlay
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.black.withValues(alpha: 0.7),
                    Colors.black.withValues(alpha: 0.3),
                    Colors.transparent,
                  ],
                  stops: const [0.0, 0.5, 1.0],
                ),
              ),
              child: Column(
                children: [
                  // Enhanced Action Bar with Logo and Controls
                  Padding(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 12),
                    child: Directionality(
                      textDirection: context.read<LocaleCubit>().isArabic()
                          ? TextDirection.rtl
                          : TextDirection.ltr,
                      child: Row(
                        children: [
                          // Back button (show when navigating from home)
                          if (widget.showBackButton)
                            Container(
                              margin: EdgeInsets.only(
                                  right: isRTL ? 2 : 6,
                                  left: isRTL ? 6 : 2),
                              decoration: BoxDecoration(
                                color: Colors.black.withValues(alpha: 0.4),
                                borderRadius: BorderRadius.circular(25),
                                border: Border.all(
                                  color: Colors.white.withValues(alpha: 0.2),
                                  width: 1,
                                ),
                              ),
                              child: IconButton(
                                onPressed: () => Navigator.pop(context),
                                icon: const Icon(
                                  Icons.arrow_back_ios_rounded,
                                  color: Colors.white,
                                  size: 20,
                                ),
                                padding: const EdgeInsets.all(8),
                              ),
                            ),

                          // Logo and App Name Section
                          Expanded(
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                // Logo Container
                                Container(
                                  padding: const EdgeInsets.all(6),
                                  decoration: BoxDecoration(
                                    color: Colors.black.withValues(alpha: 0.4),
                                    borderRadius: BorderRadius.circular(16),
                                    border: Border.all(
                                      color: AppColors.yellow
                                          .withValues(alpha: 0.3),
                                      width: 1,
                                    ),
                                  ),
                                  child: Image.asset(
                                    AppAssets.imagesLogoCircle,
                                    width: 36,
                                    height: 36,
                                  ),
                                ),

                                const SizedBox(width: 12),

                                // App Name and Section
                                Flexible(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Text(
                                        s.appName.toUpperCase(),
                                        style:
                                            AppTextStyles.font16Bold.copyWith(
                                          color: Colors.white,
                                          letterSpacing: 1.5,
                                          height: 1.1,
                                          shadows: [
                                            Shadow(
                                              color: Colors.black
                                                  .withValues(alpha: 0.5),
                                              offset: const Offset(0, 1),
                                              blurRadius: 2,
                                            ),
                                          ],
                                        ),
                                      ),
                                      Text(
                                        s.reels,
                                        style: AppTextStyles.font12Regular
                                            .copyWith(
                                          color: AppColors.yellow,
                                          letterSpacing: 0.5,
                                          height: 1.0,
                                          shadows: [
                                            Shadow(
                                              color: Colors.black
                                                  .withValues(alpha: 0.5),
                                              offset: const Offset(0, 1),
                                              blurRadius: 2,
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),

                          // Action Buttons Section (hidden for search results)
                          if (widget.searchResults.isEmpty)
                            Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                              // Search Button
                              Container(
                                decoration: BoxDecoration(
                                  color: Colors.black.withValues(alpha: 0.4),
                                  borderRadius: BorderRadius.circular(25),
                                  border: Border.all(
                                    color: Colors.white.withValues(alpha: 0.2),
                                    width: 1,
                                  ),
                                ),
                                child: IconButton(
                                  onPressed: _toggleSearchBar,
                                  icon: Icon(
                                    _showSearchBar ? Icons.close : Icons.search,
                                    color: Colors.white,
                                    size: 22,
                                  ),
                                  tooltip: s.searchReels,
                                  padding: const EdgeInsets.all(8),
                                ),
                              ),

                              const SizedBox(width: 8),

                              // Filter Button with Badge
                              Container(
                                decoration: BoxDecoration(
                                  color: Colors.black.withValues(alpha: 0.4),
                                  borderRadius: BorderRadius.circular(25),
                                  border: Border.all(
                                    color: hasActiveFilters
                                        ? AppColors.yellow
                                            .withValues(alpha: 0.6)
                                        : Colors.white.withValues(alpha: 0.2),
                                    width: 1,
                                  ),
                                ),
                                child: Stack(
                                  children: [
                                    IconButton(
                                      onPressed: _showFilterDialog,
                                      icon: Icon(
                                        Icons.filter_list,
                                        color: hasActiveFilters
                                            ? AppColors.yellow
                                            : Colors.white,
                                        size: 22,
                                      ),
                                      tooltip: s.filterReels,
                                      padding: const EdgeInsets.all(8),
                                    ),
                                    if (hasActiveFilters)
                                      Positioned(
                                        right: 4,
                                        top: 4,
                                        child: Container(
                                          width: 10,
                                          height: 10,
                                          decoration: BoxDecoration(
                                            color: AppColors.yellow,
                                            shape: BoxShape.circle,
                                            border: Border.all(
                                              color: Colors.black,
                                              width: 1,
                                            ),
                                          ),
                                        ),
                                      ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                  // Search Bar
                  if (_showSearchBar)
                    Container(
                      margin: const EdgeInsets.symmetric(horizontal: 16),
                      padding: const EdgeInsets.only(bottom: 12),
                      child: TextField(
                        controller: _searchController,
                        onChanged: _onSearchChanged,
                        onSubmitted: (_) => _onSearchSubmitted(),
                        textInputAction: TextInputAction.search,
                        style: AppTextStyles.font14Regular.copyWith(
                          color: Colors.white,
                        ),
                        decoration: InputDecoration(
                          hintText: s.searchHint,
                          hintStyle: AppTextStyles.font14Regular.copyWith(
                            color: Colors.white70,
                          ),
                          prefixIcon: const Icon(
                            Icons.search,
                            color: Colors.white70,
                          ),
                          suffixIcon: _searchQuery.isNotEmpty
                              ? Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    IconButton(
                                      onPressed: _onSearchSubmitted,
                                      icon: const Icon(
                                        Icons.search,
                                        color: AppColors.yellow,
                                      ),
                                      tooltip: s.search,
                                    ),
                                    IconButton(
                                      onPressed: () {
                                        _searchController.clear();
                                        _onSearchChanged('');
                                      },
                                      icon: const Icon(
                                        Icons.clear,
                                        color: Colors.white70,
                                      ),
                                    ),
                                  ],
                                )
                              : IconButton(
                                  onPressed: _onSearchSubmitted,
                                  icon: const Icon(
                                    Icons.search,
                                    color: Colors.white70,
                                  ),
                                  tooltip: s.search,
                                ),
                          filled: true,
                          fillColor: Colors.black.withValues(alpha: 0.3),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(25),
                            borderSide: BorderSide.none,
                          ),
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 20,
                            vertical: 12,
                          ),
                        ),
                      ),
                    ),

                  // Results Counter
                  if (hasActiveFilters)
                    Container(
                      margin: const EdgeInsets.symmetric(horizontal: 16),
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color: Colors.black.withValues(alpha: 0.5),
                        borderRadius: BorderRadius.circular(15),
                      ),
                      child: Text(
                        '${filteredVideoData.length} ${filteredVideoData.length == 1 ? 'result' : 'results'}',
                        style: AppTextStyles.font12Regular.copyWith(
                          color: Colors.white70,
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    // Set status bar based on theme
    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle(
        statusBarBrightness: isDark ? Brightness.dark : Brightness.light,
        statusBarIconBrightness: isDark ? Brightness.light : Brightness.dark,
        systemNavigationBarColor:
            isDark ? Colors.black : theme.scaffoldBackgroundColor,
        systemNavigationBarIconBrightness:
            isDark ? Brightness.light : Brightness.dark,
      ),
    );

    return BlocProvider.value(
      value: _reelsCubit,
      child: Scaffold(
        backgroundColor: isDark ? Colors.black : theme.scaffoldBackgroundColor,
        body: BlocListener<ReelsCubit, ReelsState>(
          listener: (context, state) {
            // Force update local variables when state changes
            if (state is ReelsLoaded) {
              if (mounted) {
                setState(() {
                  if (state.categories.isNotEmpty) {
                    categories = state.categories;
                  }
                  if (state.propertyTypes.isNotEmpty) {
                    propertyTypes = state.propertyTypes;
                  }
                  // Update selected IDs from cubit state
                  selectedCategoryId = state.selectedCategoryId;
                  selectedPropertyTypeId = state.selectedPropertyTypeId;
                });
              }
            }
          },
          child: BlocBuilder<ReelsCubit, ReelsState>(
            builder: (context, state) {
            if (state is ReelsLoading) {
              return _buildLoading();
            } else if (state is ReelsError) {
              return _buildError(state.message);
            } else if (state is ReelsLoaded) {
              // Update local data for video display
              filteredVideoData = state.filteredReels;
              hasInitiallyLoaded = true; // Mark that we've loaded data at least once

              // Check for empty results only after initial load is complete
              if (state.filteredReels.isEmpty) {
                return _buildNoReelsFound();
              }

              // Force update categories and property types
              if (state.categories.isNotEmpty) {
                categories = state.categories;
              }
              if (state.propertyTypes.isNotEmpty) {
                propertyTypes = state.propertyTypes;
              }

              // Always update selected IDs from cubit state
              selectedCategoryId = state.selectedCategoryId;
              selectedPropertyTypeId = state.selectedPropertyTypeId;
              return _buildReels();
            }

            // Fallback for legacy data - use cubit state for loading
            return AnimatedSwitcher(
              duration: const Duration(milliseconds: 300),
              child: (state is ReelsLoading)
                  ? _buildLoading()
                  : (state is ReelsError)
                      ? _buildError(state.message)
                      : (filteredVideoData.isEmpty && hasInitiallyLoaded)
                          ? _buildNoReelsFound()
                          : (!hasInitiallyLoaded && filteredVideoData.isEmpty)
                              ? _buildLoading() // Show loading if no initial data yet
                              : _buildReels(),
            );
          },
        ),
        ),
      ),
    );
  }

  Widget _buildFilterDialog([StateSetter? setModalState]) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final s = S.of(context);
    final isRTL = context.read<LocaleCubit>().isArabic();
    final bottomPadding = MediaQuery.of(context).padding.bottom + 16;

    return Container(
      height: MediaQuery.of(context).size.height * 0.75, // Increased height
      decoration: BoxDecoration(
        color: isDark ? AppColors.darkGrey : AppColors.white,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Directionality(
        textDirection: isRTL ? TextDirection.rtl : TextDirection.ltr,
        child: Column(
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                border: Border(
                  bottom: BorderSide(
                    color: isDark ? AppColors.darkGrey2 : AppColors.lightGrey2,
                    width: 1,
                  ),
                ),
              ),
              child: Row(
                children: [
                  const Icon(
                    Icons.filter_list,
                    color: AppColors.yellow,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      s.filterReels,
                      style: AppTextStyles.font18Bold.copyWith(
                        color: ThemeHelper.getPrimaryTextColor(context),
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: Icon(
                      Icons.close_rounded,
                      color: ThemeHelper.getSecondaryTextColor(context),
                    ),
                  ),
                ],
              ),
            ),

            // Filter Options (Scrollable Content)
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                      const SizedBox(height: 8),

                      // Categories Section
                      // Show categories section always for debugging
                      ...[
                        Text(
                          s.categories,
                          style: AppTextStyles.font16Bold.copyWith(
                            color: ThemeHelper.getPrimaryTextColor(context),
                          ),
                        ),
                        const SizedBox(height: 12),
                        Wrap(
                          spacing: 8,
                          runSpacing: 8,
                          children: [
                            _buildCategoryChip(s.all, null, isRTL),
                            if (categories.isNotEmpty)
                              ...categories.map((category) => _buildCategoryChip(
                                (category['title'] as String?) ??
                                (category['title_en'] as String?) ??
                                (category['title_ar'] as String?) ??
                                (category['name'] as String?) ??
                                'Unknown Category',
                                category['id'] as int?,
                                isRTL,
                                setModalState,
                              ))
                            else
                              const Text(
                                'No categories loaded yet...',
                                style: TextStyle(fontSize: 12, color: Colors.grey),
                              ),
                          ],
                        ),
                        const SizedBox(height: 24),
                      ],

                      // Property Types Section
                      // Show property types section always for debugging
                      ...[
                        Text(
                          s.propertyType,
                          style: AppTextStyles.font16Bold.copyWith(
                            color: ThemeHelper.getPrimaryTextColor(context),
                          ),
                        ),
                        const SizedBox(height: 12),
                        Wrap(
                          spacing: 8,
                          runSpacing: 8,
                          children: [
                            _buildPropertyTypeChip(s.all, null, isRTL),
                            if (propertyTypes.isNotEmpty)
                              ...propertyTypes.map((propertyType) => _buildPropertyTypeChip(
                                (propertyType['title'] as String?) ??
                                (propertyType['title_en'] as String?) ??
                                (propertyType['title_ar'] as String?) ??
                                (propertyType['name'] as String?) ??
                                'Unknown Type',
                                propertyType['id'] as int?,
                                isRTL,
                                setModalState,
                              ))
                            else
                              const Text(
                                'No property types loaded yet...',
                                style: TextStyle(fontSize: 12, color: Colors.grey),
                              ),
                          ],
                        ),
                        const SizedBox(height: 24),
                      ],

                      // Sort By Section
                      Text(
                        s.sortBy,
                        style: AppTextStyles.font16Bold.copyWith(
                          color: ThemeHelper.getPrimaryTextColor(context),
                        ),
                      ),
                      const SizedBox(height: 12),

                    Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children: [
                        _buildFilterChip(s.newest, 'newest', isRTL),
                        _buildFilterChip(s.oldest, 'oldest', isRTL),
                        _buildFilterChip(s.mostLiked, 'mostLiked', isRTL),
                        _buildFilterChip(
                            s.mostCommented, 'mostCommented', isRTL),
                      ],
                    ),

                  ],
                ),
              ),
            ),

            // Fixed Action Buttons at Bottom
            Container(
              padding: EdgeInsets.fromLTRB(16, 16, 16, 16 + bottomPadding),
              decoration: BoxDecoration(
                color: isDark ? AppColors.darkGrey : AppColors.white,
                border: Border(
                  top: BorderSide(
                    color: isDark ? AppColors.darkGrey2 : AppColors.lightGrey2,
                    width: 1,
                  ),
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () {
                        setState(() {
                          _selectedCategory = '';
                          _sortBy = 'newest';
                          selectedCategoryId = null;
                          selectedPropertyTypeId = null;
                          _searchController.clear();
                          _searchQuery = '';
                        });
                        // Clear cubit state selections
                        _reelsCubit.updateSelectedCategory(null);
                        _reelsCubit.updateSelectedPropertyType(null);

                        _reelsCubit.refreshReels(
                          serviceCategoryId: widget.serviceCategoryId,
                          selectedCity: selectedCity,
                        );
                        Navigator.pop(context);
                      },
                      style: OutlinedButton.styleFrom(
                        side: const BorderSide(color: AppColors.yellow),
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: Text(
                        s.clearFilter,
                        style: AppTextStyles.font14SemiBold.copyWith(
                          color: AppColors.yellow,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () {
                        // Apply filters and refresh reels with new filters
                        _reelsCubit.refreshReels(
                          serviceCategoryId: widget.serviceCategoryId,
                          selectedCity: selectedCity,
                          categoryId: selectedCategoryId,
                          propertyTypeId: selectedPropertyTypeId,
                        );
                        Navigator.pop(context);
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.yellow,
                        foregroundColor: AppColors.black,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: Text(
                        s.applyFilter,
                        style: AppTextStyles.font14SemiBold.copyWith(
                          color: AppColors.black,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFilterChip(String label, String value, bool isRTL) {
    final isSelected = _sortBy == value;

    return FilterChip(
      label: Text(
        label,
        style: AppTextStyles.font14Medium.copyWith(
          color: isSelected
              ? AppColors.black
              : ThemeHelper.getPrimaryTextColor(context),
        ),
      ),
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          _sortBy = value;
        });
      },
      selectedColor: AppColors.yellow,
      backgroundColor: Theme.of(context).brightness == Brightness.dark
          ? AppColors.darkGrey2
          : AppColors.lightGrey6,
      checkmarkColor: AppColors.black,
      side: BorderSide(
        color: isSelected ? AppColors.yellow : Colors.transparent,
        width: 1,
      ),
    );
  }

  Widget _buildCategoryChip(String label, int? categoryId, bool isRTL, [StateSetter? setModalState]) {
    final isSelected = selectedCategoryId == categoryId;

    return FilterChip(
      label: Text(
        label,
        style: AppTextStyles.font14Medium.copyWith(
          color: isSelected
              ? AppColors.black
              : ThemeHelper.getPrimaryTextColor(context),
        ),
      ),
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          selectedCategoryId = selected ? categoryId : null;
        });
        // Update modal state if available
        if (setModalState != null) {
          setModalState(() {
            selectedCategoryId = selected ? categoryId : null;
          });
        }
        // Update cubit state with selection
        _reelsCubit.updateSelectedCategory(selected ? categoryId : null);
      },
      selectedColor: AppColors.yellow,
      backgroundColor: Theme.of(context).brightness == Brightness.dark
          ? AppColors.darkGrey2
          : AppColors.lightGrey6,
      checkmarkColor: AppColors.black,
      side: BorderSide(
        color: isSelected ? AppColors.yellow : Colors.transparent,
        width: 1,
      ),
    );
  }

  Widget _buildPropertyTypeChip(String label, int? propertyTypeId, bool isRTL, [StateSetter? setModalState]) {
    final isSelected = selectedPropertyTypeId == propertyTypeId;

    return FilterChip(
      label: Text(
        label,
        style: AppTextStyles.font14Medium.copyWith(
          color: isSelected
              ? AppColors.black
              : ThemeHelper.getPrimaryTextColor(context),
        ),
      ),
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          selectedPropertyTypeId = selected ? propertyTypeId : null;
        });
        // Update modal state if available
        if (setModalState != null) {
          setModalState(() {
            selectedPropertyTypeId = selected ? propertyTypeId : null;
          });
        }
        // Update cubit state with selection
        _reelsCubit.updateSelectedPropertyType(selected ? propertyTypeId : null);
      },
      selectedColor: AppColors.yellow,
      backgroundColor: Theme.of(context).brightness == Brightness.dark
          ? AppColors.darkGrey2
          : AppColors.lightGrey6,
      checkmarkColor: AppColors.black,
      side: BorderSide(
        color: isSelected ? AppColors.yellow : Colors.transparent,
        width: 1,
      ),
    );
  }
}
